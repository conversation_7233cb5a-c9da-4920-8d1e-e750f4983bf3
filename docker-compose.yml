version: '3.8'

services:
  mcp-server:
    build:
      context: .
      dockerfile: docker/server/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - MCP_SERVER_NAME=youtube-transcript
      - MCP_SERVER_VERSION=0.1.0
      - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
      - DATABASE_URL=********************************************/youtube_transcript
      - DEBUG=${DEBUG:-0}
    volumes:
      - ./src:/app/src
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=youtube_transcript
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data: 
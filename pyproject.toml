[project]
name = "youtube_transcript"
version = "0.1.0"
description = "YouTube Transcript Server using Model Context Protocol"
requires-python = ">=3.12"
dependencies = [
    "mcp @ git+https://github.com/modelcontextprotocol/python-sdk.git",
    "pydantic>=2.6.0",
    "pydantic-settings>=2.2.0",
    "asyncpg>=0.29.0",
    "python-dotenv>=1.0.0",
    "youtube-transcript-api>=0.6.0",
    "google-api-python-client>=2.108.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.1.0",
    "black>=24.1.0",
    "isort>=5.13.0",
    "mypy>=1.8.0",
    "ruff>=0.2.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.metadata]
allow-direct-references = true

[tool.rye]
managed = true
dev-dependencies = []

[tool.ruff]
line-length = 88
target-version = "py312"
select = ["E", "F", "B", "I", "N", "UP", "PL", "RUF"]

[tool.ruff.isort]
known-first-party = ["youtube_transcript"]

[tool.mypy]
python_version = "3.12"
strict = true
ignore_missing_imports = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=src --cov-report=term-missing"

[tool.hatch.build.targets.wheel]
packages = ["src"] 
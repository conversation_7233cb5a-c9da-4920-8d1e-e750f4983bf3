#!/bin/bash

# Exit on error
set -e

# Function to print colored messages
print_message() {
    echo -e "\033[1;34m$1\033[0m"
}

print_error() {
    echo -e "\033[1;31m$1\033[0m"
}

print_success() {
    echo -e "\033[1;32m$1\033[0m"
}

# Check Python version
python_version=$(python3 --version 2>&1 | awk '{print $2}')
required_version="3.12.0"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    print_error "Error: Python 3.12 or higher is required. Current version: $python_version"
    print_message "\nTo install Python 3.12, you can use one of these methods:"
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        print_message "\n1. Using Homebrew:"
        echo "   brew install python@3.12"
        echo "   brew link python@3.12"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_message "\n1. Using apt (Ubuntu/Debian):"
        echo "   sudo add-apt-repository ppa:deadsnakes/ppa"
        echo "   sudo apt update"
        echo "   sudo apt install python3.12"
    fi
    
    print_message "\n2. Using pyenv:"
    echo "   curl https://pyenv.run | bash"
    echo "   pyenv install 3.12.0"
    echo "   pyenv global 3.12.0"
    
    print_message "\nAfter installing Python 3.12, run this script again."
    exit 1
fi

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    print_message "Installing uv..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    
    # Add uv to PATH if not already there
    if [[ ":$PATH:" != *":$HOME/.cargo/bin:"* ]]; then
        echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.zshrc
        export PATH="$HOME/.cargo/bin:$PATH"
    fi
fi

# Create virtual environment with Python 3.12
print_message "Creating virtual environment with Python 3.12..."
uv venv --python=python3.12

# Activate virtual environment
source .venv/bin/activate

# Verify Python version in virtual environment
venv_python_version=$(python --version 2>&1 | awk '{print $2}')
if [ "$(printf '%s\n' "$required_version" "$venv_python_version" | sort -V | head -n1)" != "$required_version" ]; then
    print_error "Error: Virtual environment Python version ($venv_python_version) is not 3.12 or higher"
    exit 1
fi

# Install dependencies
print_message "Installing dependencies..."
uv pip install -e ".[dev]"

# Install pre-commit hooks
print_message "Installing pre-commit hooks..."
pre-commit install

# Create Claude Desktop config directory if it doesn't exist
claude_config_dir="$HOME/Library/Application Support/Claude"
if [[ "$OSTYPE" == "darwin"* ]]; then
    mkdir -p "$claude_config_dir"
    
    # Create or update Claude Desktop config
    config_file="$claude_config_dir/claude_desktop_config.json"
    project_path=$(pwd)
    
    if [ ! -f "$config_file" ]; then
        print_message "Creating Claude Desktop configuration..."
        cat > "$config_file" << EOF
{
    "mcpServers": {
        "youtube-transcript": {
            "command": "uv",
            "args": [
                "--directory",
                "$project_path",
                "run",
                "src/server/main.py"
            ]
        }
    }
}
EOF
    else
        print_message "Claude Desktop configuration already exists. Please update it manually with:"
        echo "  - Command: uv"
        echo "  - Directory: $project_path"
        echo "  - Script: src/server/main.py"
    fi
fi

print_success "\nSetup complete! 🎉"
print_message "To activate the virtual environment, run: source .venv/bin/activate"
print_message "To start the server, run: uv run src/server/main.py" 
# Development Setup Guide

## Prerequisites
- Python 3.8 or higher
- Docker and Docker Compose
- PostgreSQL 14 or higher
- Git

## Environment Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd youtube_transcript
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install development dependencies:
```bash
pip install -e ".[dev]"
```

## Database Setup

1. Start PostgreSQL using Docker Compose:
```bash
docker-compose up -d postgres
```

2. Create database and tables:
```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U postgres

# Create database
CREATE DATABASE youtube_transcript;

# Connect to database
\c youtube_transcript

# Run schema.sql
\i /docker-entrypoint-initdb.d/schema.sql
```

## Development Server

1. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

2. Start the development server:
```bash
# Start all services
docker-compose up -d

# Or start just the MCP server
python src/server.py
```

3. Run tests:
```bash
pytest
```

## MCP Client Development

1. Create a new client:
```python
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def main():
    server_params = StdioServerParameters(
        command="python",
        args=["src/server.py"],
        env=None,
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            # Your client code here
```

2. Test the client:
```bash
python src/client.py
```

## Development Workflow

1. Code Structure:
```
src/
├── server/          # MCP server implementation
├── prompts/         # Prompt definitions
├── tools/          # Tool implementations
└── types/          # Type definitions
```

2. Adding New Features:
   - Create new prompt in `src/prompts/`
   - Implement tool in `src/tools/`
   - Add tests in `tests/`
   - Update documentation

3. Testing:
   - Unit tests: `pytest tests/`
   - Integration tests: `pytest tests/integration/`
   - Type checking: `mypy src/`

## Debugging

1. Server Logs:
```bash
docker-compose logs -f mcp-server
```

2. Database Logs:
```bash
docker-compose logs -f postgres
```

3. Debug Mode:
```bash
# Set DEBUG=1 in .env
# Or run with debug flag
python src/server.py --debug
```

## Common Issues

1. Database Connection:
   - Check PostgreSQL is running
   - Verify connection string in .env
   - Check network connectivity

2. MCP Server:
   - Verify Python version
   - Check dependencies
   - Review server logs

3. Client Issues:
   - Check server is running
   - Verify prompt names
   - Review client logs

## Contributing

1. Create a new branch:
```bash
git checkout -b feature/your-feature
```

2. Make changes and commit:
```bash
git add .
git commit -m "feat: your feature description"
```

3. Push and create PR:
```bash
git push origin feature/your-feature
```

## Additional Resources

- [MCP Protocol Documentation](https://github.com/modelcontextprotocol/python-sdk)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker Documentation](https://docs.docker.com/) 
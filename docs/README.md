# YouTube Transcript MCP Server Documentation

## Overview
This project implements a Model Context Protocol (MCP) server for fetching and processing YouTube video transcripts.

## Documentation Structure

### Design Documents
- [Architecture Design](design/architecture.md) - Overall system architecture
- [MCP Protocol](design/mcp-protocol.md) - MCP protocol implementation details
- [API Specification](design/api-spec.md) - API endpoints and specifications

### Development Guides
- [Setup Guide](development/setup.md) - Development environment setup
- [Contributing Guide](development/contributing.md) - How to contribute to the project

## Quick Start
1. Clone the repository
2. Follow the [Setup Guide](development/setup.md)
3. Start the server using Docker Compose:
```bash
docker-compose up -d
``` 
# MCP Server Quickstart Guide

## Prerequisites

- Python 3.12 or higher
- `uv` package manager
- Basic understanding of Python and LLMs

## Setup

1. Install `uv`:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. Create and activate virtual environment:
```bash
uv venv
source .venv/bin/activate
```

3. Install dependencies:
```bash
uv pip install -e ".[dev]"
```

## Core MCP Concepts

MCP servers can provide three main types of capabilities:

1. **Resources**: File-like data that can be read by clients
2. **Tools**: Functions that can be called by the LLM (with user approval)
3. **Prompts**: Pre-written templates that help users accomplish specific tasks

## Server Implementation

### Basic Server Setup

```python
from mcp.server.fastmcp import FastMCP

# Initialize FastMCP server
mcp = FastMCP("youtube-transcript")

# Run the server
if __name__ == "__main__":
    mcp.run(transport='stdio')
```

### Tool Implementation

```python
@mcp.tool()
async def get_transcript(video_url: str, language: str = "en") -> str:
    """Get transcript for a YouTube video.

    Args:
        video_url: URL of the YouTube video
        language: Language code for the transcript (default: en)
    """
    # Implementation here
    pass
```

## Testing with Claude for Desktop

1. Install Claude for Desktop from the [official website](https://claude.ai/desktop)

2. Configure Claude for Desktop by editing `~/Library/Application Support/Claude/claude_desktop_config.json`:
```json
{
    "mcpServers": {
        "youtube-transcript": {
            "command": "uv",
            "args": [
                "--directory",
                "/ABSOLUTE/PATH/TO/PROJECT",
                "run",
                "src/server/main.py"
            ]
        }
    }
}
```

3. Restart Claude for Desktop

## Troubleshooting

### Common Issues

1. **Server not showing up in Claude**
   - Check `claude_desktop_config.json` syntax
   - Verify absolute paths
   - Restart Claude for Desktop

2. **Tool calls failing**
   - Check Claude's logs in `~/Library/Logs/Claude/mcp*.log`
   - Verify server builds and runs without errors
   - Restart Claude for Desktop

### Debugging

1. Check logs:
```bash
tail -n 20 -f ~/Library/Logs/Claude/mcp*.log
```

2. Verify server:
```bash
uv run src/server/main.py
```

## Next Steps

1. Add more tools to your server
2. Implement error handling
3. Add tests
4. Set up CI/CD

## Resources

- [MCP Documentation](https://modelcontextprotocol.io)
- [Python SDK Reference](https://modelcontextprotocol.io/python-sdk)
- [Example Servers](https://modelcontextprotocol.io/examples) 
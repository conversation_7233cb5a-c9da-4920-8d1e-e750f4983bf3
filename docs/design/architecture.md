# Architecture Design

## System Overview
The YouTube Transcript MCP Server is designed to provide transcript services through the Model Context Protocol (MCP). The system uses PostgreSQL for persistent storage of transcripts and metadata, providing efficient data retrieval and management.

## Components

### 1. MCP Server
- Entry point for all MCP protocol interactions
- Handles prompt, tool, and resource primitives
- Manages server lifecycle and configuration
- Implements async request handling
- Provides health check endpoints

### 2. Prompt Handler
- Processes transcript-related prompts
- Validates input parameters
- Coordinates with tools for transcript fetching
- Implements prompt templates
- Handles prompt versioning

### 3. Tool Handler
- YouTube API integration
- Video ID extraction
- Transcript formatting
- Error handling and retries
- Rate limiting implementation

### 4. Database Layer (PostgreSQL)
#### Schema Design
```sql
-- Transcripts table
CREATE TABLE transcripts (
    id SERIAL PRIMARY KEY,
    video_id VARCHAR(20) NOT NULL,
    language VARCHAR(10) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(video_id, language)
);

-- Transcript segments table
CREATE TABLE transcript_segments (
    id SERIAL PRIMARY KEY,
    transcript_id INTEGER REFERENCES transcripts(id),
    start_time FLOAT NOT NULL,
    end_time FLOAT NOT NULL,
    text TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Video metadata table
CREATE TABLE video_metadata (
    id SERIAL PRIMARY KEY,
    video_id VARCHAR(20) NOT NULL UNIQUE,
    title TEXT,
    duration INTEGER,
    channel_id VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### Database Operations
- Transcript storage and retrieval
- Metadata caching
- Batch operations support
- Data consistency checks
- Automatic cleanup of old data

### 5. Docker Container
- Isolated runtime environment
- Environment configuration
- Health monitoring
- Resource limits
- Network configuration

## Data Flow
1. Client sends MCP request
2. Server validates and processes request
3. Database check for cached transcript
4. If not cached:
   - Tools fetch transcript from YouTube
   - Store in PostgreSQL
   - Return formatted response
5. If cached:
   - Retrieve from PostgreSQL
   - Return formatted response

## Error Handling
- Input validation
- YouTube API errors
- Network issues
- Rate limiting
- Database connection errors
- Data consistency errors
- Retry mechanisms

## Security
- Environment variable management
- API key protection
- Input sanitization
- Database access control
- Network security
- Rate limiting per client
- SQL injection prevention

## Monitoring
- Health checks
- Logging
- Error tracking
- Database performance metrics
- API usage statistics
- Cache hit/miss rates
- Response time monitoring

## Deployment Architecture
```
┌─────────────────┐     ┌─────────────────┐
│   MCP Server    │────▶│   PostgreSQL    │
└────────┬────────┘     └─────────────────┘
         │
         ▼
┌─────────────────┐
│  YouTube API    │
└─────────────────┘
```

## Database Indexing Strategy
- Primary indexes on video_id and language
- Composite indexes for common queries
- Partial indexes for active data
- Regular index maintenance

## Cache Invalidation Strategy
- TTL-based expiration
- Manual invalidation for updates
- Batch cleanup operations
- Version-based invalidation

## Performance Considerations
- Connection pooling
- Query optimization
- Batch operations
- Async database operations
- Index usage monitoring
- Regular maintenance tasks 
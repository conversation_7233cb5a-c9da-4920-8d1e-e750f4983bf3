import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def main():
    # Create server parameters for stdio connection
    server_params = StdioServerParameters(
        command="python",
        args=["-m", "src.server.main"],
        env=None,
    )

    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            # Initialize the connection
            await session.initialize()

            # List available prompts
            prompts = await session.list_prompts()
            print("Available prompts:", prompts)

            # Get a prompt
            prompt = await session.get_prompt(
                "get-transcript",
                arguments={"video_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"}
            )
            print("Prompt result:", prompt)

if __name__ == "__main__":
    asyncio.run(main()) 
import logging
from typing import Optional
import asyncpg
from datetime import datetime, timedelta

from src.server.exceptions import DatabaseError, TranscriptError
from src.server.config import Settings

logger = logging.getLogger(__name__)

class TranscriptManager:
    """Manages transcript storage and retrieval."""
    
    def __init__(self, database_url: str):
        """Initialize transcript manager."""
        self.database_url = database_url
        self.pool = None
    
    async def initialize(self):
        """Initialize database connection pool."""
        try:
            self.pool = await asyncpg.create_pool(self.database_url)
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            raise DatabaseError(f"Database initialization failed: {str(e)}")
    
    async def cleanup(self):
        """Clean up database connections."""
        if self.pool:
            await self.pool.close()
    
    async def get_transcript(self, video_id: str, language: str = "en") -> str:
        """Get transcript from cache or fetch new one."""
        try:
            # Check cache
            transcript = await self._get_cached_transcript(video_id, language)
            if transcript:
                return transcript
            
            # Fetch new transcript
            from .youtube import YouTubeClient
            settings = Settings()
            youtube_client = YouTubeClient(settings.youtube_api_key)
            
            transcript = await youtube_client.get_transcript(video_id, language)
            
            # Cache transcript
            await self._cache_transcript(video_id, language, transcript)
            
            return transcript
            
        except Exception as e:
            logger.error(f"Error getting transcript: {e}")
            raise TranscriptError(f"Failed to get transcript: {str(e)}")
    
    async def _get_cached_transcript(self, video_id: str, language: str) -> Optional[str]:
        """Get transcript from cache."""
        try:
            async with self.pool.acquire() as conn:
                # Check if transcript exists and is not expired
                row = await conn.fetchrow("""
                    SELECT t.id, ts.text
                    FROM transcripts t
                    JOIN transcript_segments ts ON t.id = ts.transcript_id
                    WHERE t.video_id = $1 AND t.language = $2
                    AND t.updated_at > $3
                    ORDER BY ts.start_time
                """, video_id, language, datetime.utcnow() - timedelta(seconds=3600))
                
                if row:
                    return row['text']
                return None
                
        except Exception as e:
            logger.error(f"Error getting cached transcript: {e}")
            return None
    
    async def _cache_transcript(self, video_id: str, language: str, transcript: str):
        """Cache transcript in database."""
        try:
            async with self.pool.acquire() as conn:
                async with conn.transaction():
                    # Insert or update transcript
                    transcript_id = await conn.fetchval("""
                        INSERT INTO transcripts (video_id, language)
                        VALUES ($1, $2)
                        ON CONFLICT (video_id, language) DO UPDATE
                        SET updated_at = CURRENT_TIMESTAMP
                        RETURNING id
                    """, video_id, language)
                    
                    # Insert transcript segments
                    await conn.execute("""
                        INSERT INTO transcript_segments (transcript_id, start_time, end_time, text)
                        VALUES ($1, $2, $3, $4)
                    """, transcript_id, 0, 0, transcript)
                    
        except Exception as e:
            logger.error(f"Error caching transcript: {e}")
            raise DatabaseError(f"Failed to cache transcript: {str(e)}") 
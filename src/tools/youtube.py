import re
from youtube_transcript_api import YouTubeTranscriptApi, TranscriptsDisabled, NoTranscriptFound
from src.server.exceptions import YouTubeError

class YouTubeClient:
    def __init__(self, api_key: str = None):
        self.api_key = api_key

    @staticmethod
    def extract_video_id(url: str) -> str:
        # Simple regex for YouTube video ID extraction
        patterns = [
            r"(?:v=|youtu\.be/|embed/|shorts/)([\w-]{11})",
            r"youtube\.com/watch\?v=([\w-]{11})"
        ]
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        if re.match(r"^[\w-]{11}$", url):
            return url
        raise YouTubeError(f"Invalid YouTube URL or video ID: {url}")

    def get_transcript(self, video_id: str, language: str = "en"):
        try:
            transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=[language])
            return transcript
        except (TranscriptsDisabled, NoTranscriptFound) as e:
            # Try fallback to English if not found
            if language != "en":
                try:
                    return YouTubeTranscriptApi.get_transcript(video_id, languages=["en"])
                except Exception as e2:
                    raise YouTubeError(f"Transcript not available: {e2}")
            raise YouTubeError(f"Transcript not available: {e}")
        except Exception as e:
            raise YouTubeError(f"Error fetching transcript: {e}")

    def get_video_metadata(self, video_id: str):
        # Placeholder for YouTube Data API call
        # Implement as needed
        return {} 
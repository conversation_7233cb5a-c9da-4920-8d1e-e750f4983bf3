import asyncio
from collections.abc import AsyncIterator
import logging

from mcp.server import Server
import mcp.server.stdio
from mcp.server.lowlevel import NotificationOptions
from mcp.server.models import InitializationOptions
import mcp.types as types

from src.server.config import Settings
from src.server.exceptions import MCPError
from src.tools.youtube import YouTubeClient
from src.tools.transcript import TranscriptManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a server instance
server = Server("youtube-transcript-server")

# Initialize resources at module level
settings = Settings()
youtube_client = YouTubeClient(settings.youtube_api_key)
transcript_manager = TranscriptManager(settings.database_url)

@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """List available prompts."""
    return [
        types.Prompt(
            name="get-transcript",
            description="Get transcript for a YouTube video",
            arguments=[
                types.PromptArgument(
                    name="video_url",
                    description="YouTube video URL",
                    required=True
                ),
                types.PromptArgument(
                    name="language",
                    description="Transcript language code",
                    required=False
                )
            ],
        )
    ]

@server.get_prompt()
async def handle_get_prompt(
    name: str,
    arguments: dict[str, str] | None
) -> types.GetPromptResult:
    """Handle prompt requests."""
    if name != "get-transcript":
        raise MCPError(f"Unknown prompt: {name}")

    if not arguments or "video_url" not in arguments:
        raise MCPError("video_url is required")

    video_url = arguments["video_url"]
    language = arguments.get("language", "en")

    try:
        # Extract video ID
        video_id = await youtube_client.extract_video_id(video_url)
        # Ensure transcript_manager is initialized
        if not getattr(transcript_manager, "pool", None):
            await transcript_manager.initialize()
        # Get or fetch transcript
        transcript = await transcript_manager.get_transcript(video_id, language)
        return types.GetPromptResult(
            description="YouTube video transcript",
            messages=[
                types.PromptMessage(
                    role="assistant",
                    content=types.TextContent(
                        type="text",
                        text=transcript
                    ),
                )
            ],
        )
    except Exception as e:
        logger.error(f"Error processing transcript: {e}")
        raise MCPError(f"Failed to get transcript: {str(e)}")

async def run():
    """Run the MCP server."""
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="youtube-transcript",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(run()) 
from pydantic_settings import BaseSettings
from functools import lru_cache

class Settings(BaseSettings):
    """Server configuration settings."""
    
    # Server settings
    mcp_server_name: str = "youtube-transcript"
    mcp_server_version: str = "0.1.0"
    
    # YouTube API settings
    youtube_api_key: str
    
    # Database settings
    database_url: str = "********************************************/youtube_transcript"
    
    # Cache settings
    cache_ttl: int = 3600  # 1 hour
    
    class Config:
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings() 